import uvicorn
from api.main import app

import os

os.environ['CELERY_BROKER_URL'] = 'redis://:celeryhub123@localhost:6389/0'
os.environ['CELERY_RESULT_BACKEND'] = 'redis://:celeryhub123@localhost:6389/1'

def main():
    """启动Celery Hub API服务器"""
    print("正在启动Celery Hub...")
    uvicorn.run(
        "api.main:app",
        host="0.0.0.0",
        port=8004,
        # reload=True,
        log_level="info"
    )


if __name__ == "__main__":
    main()
