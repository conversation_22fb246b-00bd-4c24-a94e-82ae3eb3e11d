import inspect
from celery import Celery
from fastapi import APIRouter, Depends, Body, HTTPException
from api.dependencies import get_celery_app
from api.database import (
    save_callback_config, get_callback_url, get_all_callback_configs, delete_callback_config,
    add_task_to_monitoring_queue
)

from typing import Type
from pydantic import BaseModel, HttpUrl

router = APIRouter(prefix="/v1")

# Pydantic 模型
class CallbackRequest(BaseModel):
    appId: str
    callback_url: HttpUrl

class CallbackResponse(BaseModel):
    success: bool
    message: str
    appId: str
    callback_url: str = None


def get_summary(description):
    v = description.split('\n')
    for i in v:
        if i.strip():
            return i.strip()
                

def auto_generate_task_endpoints():
    """自动扫描所有任务并生成API端点"""
    app = get_celery_app()
    registered_tasks = app.tasks.keys()
    

    # 获取所有任务的名称
    task_names = [task_name for task_name in registered_tasks if task_name.startswith('sskw.')]

    for task_name in task_names:
        if not task_name.startswith('sskw.'):
            continue
            
        # 获取任务函数
        task_func = app.tasks[task_name]
        
        # 从文档字符串提取描述
        description = task_func.__doc__ or f"Execute {task_name} task"
        
        # 创建动态Pydantic模型
        input_model = None
        if hasattr(task_func, '__annotations__'):
            input_annotation = task_func.__annotations__.get('input')
            if input_annotation and inspect.isclass(input_annotation):
                input_model = input_annotation
        
        # 为任务创建路由
        task_short_name = '_'.join(task_name.split('.')[1:])

        # 创建闭包来捕获当前循环的变量
        def create_task_endpoint(
            current_queue_name=task_name, 
            current_input_model=input_model, 
            endpoint_name=task_short_name,
            endpoint_description=description,
            endpoint_summary=get_summary(description),
            task_count=len(task_names)
        ):
            async def task_endpoint(
                queue: str = Body(...),
                appId: str = Body(...),
                inputs: current_input_model = Body(...) if current_input_model else Body({}),
                celery_app: Celery = Depends(get_celery_app)
            ):
                # 发送任务到Celery
                task = celery_app.send_task(current_queue_name, queue = queue, args=[inputs.dict() if hasattr(inputs, 'dict') else inputs])
                
                # 将任务添加到监听队列
                try:
                    task_inputs = inputs.dict() if hasattr(inputs, 'dict') else inputs
                    success = add_task_to_monitoring_queue(task.id, appId, queue, task_inputs)
                    if not success:
                        print(f"警告：任务 {task.id} 添加到监听队列失败")
                except Exception as e:
                    print(f"错误：将任务 {task.id} 添加到监听队列时发生异常: {e}")
                
                return {
                    "task_id": task.id,
                    "status": "queued",
                    "monitor_url": f"/tasks/{task.id}/status",
                    "app_id": appId
                }
            
            # 手动注册路由
            router.add_api_route(
                f"/task/{endpoint_name}",
                task_endpoint,
                methods=["POST"],
                name=endpoint_name,
                description=endpoint_description,
                tags=[f"Tasks({task_count})"],
                summary=endpoint_summary
            )
            
            return task_endpoint
        
        # 调用闭包创建端点
        create_task_endpoint()

# 在导入时自动生成路由
auto_generate_task_endpoints()

# 添加回调配置端点
@router.post("/stask/callback", 
             response_model=CallbackResponse,
             tags=["Callback Management"], 
             summary="配置应用回调地址",
             description="为指定的appId配置回调地址")
async def set_callback(request: CallbackRequest):
    """
    配置应用回调地址
    
    - **appId**: 应用唯一标识符
    - **callback_url**: 回调地址URL
    """
    try:
        success = save_callback_config(request.appId, str(request.callback_url))
        if success:
            return CallbackResponse(
                success=True,
                message="回调配置保存成功",
                appId=request.appId,
                callback_url=str(request.callback_url)
            )
        else:
            raise HTTPException(status_code=500, detail="保存回调配置失败")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"配置回调失败: {str(e)}")

@router.get("/stask/callback/{app_id}",
            tags=["Callback Management"],
            summary="获取应用回调地址",
            description="根据appId获取配置的回调地址")
async def get_callback(app_id: str):
    """获取指定appId的回调地址"""
    callback_url = get_callback_url(app_id)
    if callback_url:
        return {
            "success": True,
            "appId": app_id,
            "callback_url": callback_url
        }
    else:
        raise HTTPException(status_code=404, detail=f"未找到appId '{app_id}' 的回调配置")

@router.get("/stask/callbacks",
            tags=["Callback Management"],
            summary="获取所有回调配置",
            description="获取所有已配置的应用回调地址")
async def get_all_callbacks():
    """获取所有回调配置"""
    callbacks = get_all_callback_configs()
    return {
        "success": True,
        "count": len(callbacks),
        "callbacks": callbacks
    }

@router.delete("/stask/callback/{app_id}",
               tags=["Callback Management"],
               summary="删除应用回调配置",
               description="删除指定appId的回调配置")
async def delete_callback(app_id: str):
    """删除指定appId的回调配置"""
    success = delete_callback_config(app_id)
    if success:
        return {
            "success": True,
            "message": f"已删除appId '{app_id}' 的回调配置",
            "appId": app_id
        }
    else:
        raise HTTPException(status_code=404, detail=f"未找到appId '{app_id}' 的回调配置或删除失败")

# 任务监控相关端点
@router.get("/monitor/status",
            tags=["Task Monitoring"],
            summary="获取监控器状态",
            description="获取任务监控器的运行状态")
async def get_monitor_status():
    """获取监控器状态"""
    from api.task_monitor import get_monitor_status
    status = get_monitor_status()
    return {
        "success": True,
        "monitor_status": status
    }

@router.get("/monitor/tasks/{task_id}",
            tags=["Task Monitoring"],
            summary="获取任务详细信息",
            description="获取指定任务的详细监控信息")
async def get_task_details(task_id: str):
    """获取任务详细信息"""
    from api.database import get_task_info
    task_info = get_task_info(task_id)
    if task_info:
        return {
            "success": True,
            "task": task_info
        }
    else:
        raise HTTPException(status_code=404, detail=f"未找到任务 '{task_id}' 的监控信息")

@router.get("/monitor/tasks",
            tags=["Task Monitoring"],
            summary="获取任务监控列表",
            description="获取所有正在监控的任务列表")
async def get_monitoring_tasks(
    status: str = None,
    app_id: str = None,
    limit: int = 100,
    offset: int = 0
):
    """获取任务监控列表"""
    from api.database import db
    
    try:
        with db._get_connection() as conn:
            # 构建查询条件
            where_conditions = []
            params = []
            
            if status:
                where_conditions.append("task_status = ?")
                params.append(status)
            
            if app_id:
                where_conditions.append("app_id = ?")
                params.append(app_id)
            
            where_clause = ""
            if where_conditions:
                where_clause = "WHERE " + " AND ".join(where_conditions)
            
            # 查询任务列表
            query = f"""
                SELECT * FROM task_monitoring 
                {where_clause}
                ORDER BY created_at DESC 
                LIMIT ? OFFSET ?
            """
            params.extend([limit, offset])
            
            cursor = conn.execute(query, params)
            tasks = [dict(row) for row in cursor.fetchall()]
            
            # 查询总数
            count_query = f"SELECT COUNT(*) FROM task_monitoring {where_clause}"
            count_params = params[:-2]  # 移除limit和offset参数
            cursor = conn.execute(count_query, count_params)
            total = cursor.fetchone()[0]
            
            return {
                "success": True,
                "tasks": tasks,
                "total": total,
                "limit": limit,
                "offset": offset
            }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务列表失败: {str(e)}")

@router.post("/monitor/start",
             tags=["Task Monitoring"],
             summary="启动任务监控器",
             description="手动启动任务监控器（通常在应用启动时自动启动）")
async def start_monitor():
    """启动任务监控器"""
    try:
        from api.task_monitor import start_task_monitor
        start_task_monitor()
        return {
            "success": True,
            "message": "任务监控器启动成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"启动监控器失败: {str(e)}")

@router.post("/monitor/stop",
             tags=["Task Monitoring"],
             summary="停止任务监控器",
             description="手动停止任务监控器")
async def stop_monitor():
    """停止任务监控器"""
    try:
        from api.task_monitor import stop_task_monitor
        stop_task_monitor()
        return {
            "success": True,
            "message": "任务监控器停止成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"停止监控器失败: {str(e)}")


