[project]
name = "celeryhub"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "build>=1.2.2.post1",
    "celery>=5.5.3",
    "fastapi[all]>=0.115.14",
    "flower>=2.0.1",
    "pydantic>=2.0.0",
    "redis>=4.5.0",
    "requests>=2.31.0",
    "setuptools>=80.9.0",
    "sskw-tasks>=0.4.0",
    "twine>=6.1.0",
    "wheel>=0.45.1",
]

[tool.pytest.ini_options]
pythonpath = ["."]
